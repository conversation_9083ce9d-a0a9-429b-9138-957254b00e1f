import { Component, OnInit } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'jedsy';

  constructor(private keycloakService: KeycloakService) {}

  ngOnInit() {
    console.log('AppComponent initialized');

    this.keycloakService.keycloakEvents$.subscribe({
      next: (event) => {
        console.log('Keycloak event', event);
      },
      error: (error) => console.error('Keycloak event error', error),
    });

    // Check if Keycloak is initialized
    this.keycloakService.isLoggedIn().then(isLoggedIn => {
      console.log('User logged in:', isLoggedIn);
    }).catch(error => {
      console.error('Error checking login status:', error);
    });
  }
}
