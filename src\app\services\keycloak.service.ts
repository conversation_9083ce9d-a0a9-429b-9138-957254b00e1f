// src/app/services/keycloak.service.ts

import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import keycloakConfig from '../../environments/keycloak.config';
import Keycloak from 'keycloak-js';

@Injectable({
  providedIn: 'root',
})
export class KeycloakInitService {
    private keycloakService: KeycloakService;
  constructor(private keycloak: KeycloakService) { this.keycloakService = keycloak; }

  init(): Promise<boolean> {
    console.log('Initializing Keycloak with config:', keycloakConfig);

    // For development: check if we're in local environment
    const isLocalDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

    if (isLocalDevelopment) {
      console.log('Running in local development mode - skipping Keycloak authentication');
      // Return a resolved promise to bypass authentication in development
      return Promise.resolve(true);
    }

    return this.keycloakService.init({
      config: keycloakConfig,
      initOptions: {
        onLoad: 'login-required',
        checkLoginIframe: false, // Disable iframe check for development
      },
      enableBearerInterceptor: true,
      bearerExcludedUrls: ['/assets'],
    }).catch(error => {
      console.error('Keycloak initialization failed:', error);
      console.log('Falling back to development mode without authentication');
      // Fallback to development mode if Keycloak fails
      return Promise.resolve(true);
    });
  }
}
