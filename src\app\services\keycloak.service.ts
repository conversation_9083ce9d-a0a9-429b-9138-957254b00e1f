// src/app/services/keycloak.service.ts

import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';
import keycloakConfig from '../../environments/keycloak.config';
import Keycloak from 'keycloak-js';

@Injectable({
  providedIn: 'root',
})
export class KeycloakInitService {
    private keycloakService: KeycloakService;
  constructor(private keycloak: KeycloakService) { this.keycloakService = keycloak; }

  init(): Promise<boolean> {
    console.log('Initializing Keycloak with config:', keycloakConfig);

    return this.keycloakService.init({
      config: keycloakConfig,
      initOptions: {
        onLoad: 'login-required',
        checkLoginIframe: false, // Disable iframe check for development
      },
      enableBearerInterceptor: true,
      bearerExcludedUrls: ['/assets'],
    }).catch(error => {
      console.error('Keycloak initialization failed:', error);
      throw error;
    });
  }
}
