import { Injectable } from '@angular/core';
import { KeycloakService } from 'keycloak-angular';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(private keycloakService: KeycloakService) {}

  login() {
    this.keycloakService.login();
  }

  logout() {
    this.keycloakService.logout(window.location.origin);
  }

  async isLoggedIn(): Promise<boolean> {
    const isLocalDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    if (isLocalDevelopment) {
      return true; // Always return true in development mode
    }
    try {
      return await this.keycloakService.isLoggedIn();
    } catch (error) {
      console.error('Error checking login status:', error);
      return true; // Fallback to true for development
    }
  }

  getUsername(): string {
    return this.keycloakService.getUsername();
  }

  getUserRoles(): string[] {
    return this.keycloakService.getUserRoles();
  }

  hasRole(role: string): boolean {
    const userRoles = this.keycloakService.getUserRoles();
    return userRoles.some(userRole =>
      userRole === role
    );
  }
}
